# Roles Management API Implementation

## Overview

This document provides a detailed implementation plan for the Roles Management API based on the database schema defined in `/workspace/schemas/data-core-pg/1.0/DDL/schema.sql`. The implementation follows the established patterns in the onramp codebase and provides CRUD operations for custom roles and role template management.

## Database Schema Analysis

### Key Tables
- **CustomRole**: Organization-specific roles derived from template roles
- **TemplateRole**: Predefined role templates for organization types
- **Organization**: Core organizations with OrgTypeIdentifier
- **OrgType**: Types of organizations (synapse, municipality, oem)
- **Permission**: Individual permissions scoped by organization type
- **CustomRolePermission**: Permission assignments for custom roles
- **TemplateRolePermission**: Default permission assignments for templates

### Relationships
```
Organization -> OrgType (via OrgTypeIdentifier)
TemplateRole -> OrgType (via OrgTypeIdentifier)
CustomRole -> Organization (via OrganizationId)
CustomRole -> TemplateRole (via TemplateRoleIdentifier)
CustomRole -> OrgType (via OrgTypeIdentifier)
```

## API Endpoints

### 1. GET /api/organization/{organizationId}/roles
**Purpose**: Retrieve all roles for a given organization

**Request Parameters**:
- `organizationId` (UUID): Organization identifier in URL path

**Response Structure**:
```json
{
  "status": "success",
  "data": [
    {
      "id": "uuid",
      "organizationId": "uuid",
      "templateRoleIdentifier": "string",
      "orgTypeIdentifier": "string",
      "name": "string",
      "description": "string",
      "createdAt": "timestamp",
      "updatedAt": "timestamp"
    }
  ],
  "message": "Request Succeeded",
  "code": 200
}
```

**Database Query**:
```sql
SELECT 
    cr.Id,
    cr.OrganizationId,
    cr.TemplateRoleIdentifier,
    cr.OrgTypeIdentifier,
    cr.Name,
    cr.Description,
    cr.CreatedAt,
    cr.UpdatedAt
FROM {{CustomRole}} cr
WHERE cr.OrganizationId = $1 AND cr.IsDeleted = false
ORDER BY cr.CreatedAt DESC
```

### 2. DELETE /api/organization/{organizationId}/roles/{roleId}
**Purpose**: Delete a custom role from the organization

**Request Parameters**:
- `organizationId` (UUID): Organization identifier in URL path
- `roleId` (UUID): Role identifier in URL path

**Response Structure**:
```json
{
  "status": "success",
  "data": null,
  "message": "Role deleted successfully",
  "code": 200
}
```

**Database Query**:
```sql
UPDATE {{CustomRole}} 
SET IsDeleted = true, UpdatedAt = NOW()
WHERE Id = $1 AND OrganizationId = $2 AND IsDeleted = false
```

### 3. POST /api/organization/{organizationId}/roles
**Purpose**: Create a new custom role for the organization

**Request Parameters**:
- `organizationId` (UUID): Organization identifier in URL path

**Request Body**:
```json
{
  "name": "string",
  "templateRoleIdentifier": "string",
  "description": "string"
}
```

**Response Structure**:
```json
{
  "status": "success",
  "data": {
    "id": "uuid",
    "organizationId": "uuid",
    "templateRoleIdentifier": "string",
    "orgTypeIdentifier": "string",
    "name": "string",
    "description": "string",
    "createdAt": "timestamp",
    "updatedAt": "timestamp"
  },
  "message": "Request Succeeded",
  "code": 200
}
```

**Database Operations**:
1. Validate organization exists and get OrgTypeIdentifier
2. Validate template role exists and matches organization type
3. Insert new CustomRole
4. Copy permissions from TemplateRolePermission to CustomRolePermission

### 4. GET /api/organization/{organizationId}/role-templates
**Purpose**: Retrieve role templates by organization type

**Request Parameters**:
- `organizationId` (UUID): Organization identifier in URL path

**Response Structure**:
```json
{
  "status": "success",
  "data": [
    {
      "identifier": "string",
      "name": "string",
      "orgTypeIdentifier": "string",
      "description": "string"
    }
  ],
  "message": "Request Succeeded",
  "code": 200
}
```

**Database Query**:
```sql
SELECT 
    tr.Identifier,
    tr.Name,
    tr.OrgTypeIdentifier,
    tr.Description
FROM {{TemplateRole}} tr
JOIN {{Organization}} o ON o.OrgTypeIdentifier = tr.OrgTypeIdentifier
WHERE o.Id = $1 AND o.IsDeleted = false
ORDER BY tr.Name ASC
```

## Implementation Structure

### File Organization
```
/workspace/shared/rest/onramp/roles/
├── roles.go          # HTTP handlers and business logic
├── schemas.go        # Request/response structures
├── errors.go         # Error definitions
└── implementation.md # This document
```

### Dependencies
- `github.com/google/uuid` - UUID generation and parsing
- `github.com/gorilla/mux` - HTTP routing for path parameters
- `synapse-its.com/shared/connect` - Database connection management
- `synapse-its.com/shared/api/response` - Standardized API responses
- `synapse-its.com/shared/logger` - Logging utilities
- `synapse-its.com/shared/rest/onramp/helper` - Validation helpers

### Error Handling
Following the established pattern, define specific errors:
- `ErrDatabaseOperation` - Database operation failures
- `ErrRoleNotFound` - Role not found
- `ErrOrganizationNotFound` - Organization not found
- `ErrTemplateRoleNotFound` - Template role not found
- `ErrInvalidOrganizationId` - Invalid organization UUID
- `ErrInvalidRoleId` - Invalid role UUID
- `ErrInvalidRequestBody` - Invalid JSON request body
- `ErrUnexpectedFields` - Unexpected fields in request
- `ErrInvalidRoleName` - Empty or invalid role name
- `ErrInvalidDescription` - Empty description
- `ErrTemplateRoleMismatch` - Template role doesn't match organization type

### Validation Patterns
1. **UUID Validation**: Use `uuid.Parse()` for all UUID parameters
2. **Organization Validation**: Verify organization exists and is not deleted
3. **Template Role Validation**: Ensure template role matches organization type
4. **Request Body Validation**: Use `decoder.DisallowUnknownFields()`
5. **String Validation**: Use `strings.TrimSpace()` for non-empty checks

### Database Transaction Patterns
For role creation, use transactions to ensure data consistency:
1. Begin transaction
2. Validate organization and template role
3. Insert CustomRole
4. Copy permissions from template
5. Commit transaction

### Testing Strategy
1. **Unit Tests**: Test individual functions with mocked dependencies
2. **Integration Tests**: Test database operations with test database
3. **Handler Tests**: Test HTTP handlers with mock requests
4. **Error Cases**: Test all error conditions and edge cases

## Security Considerations

1. **Authorization**: Implement proper authorization checks (future enhancement)
2. **Input Validation**: Validate all inputs to prevent injection attacks
3. **UUID Validation**: Ensure UUIDs are properly formatted
4. **Organization Scope**: Ensure users can only access their organization's roles
5. **Soft Deletes**: Use soft deletes to maintain audit trail

## Performance Considerations

1. **Database Indexes**: Leverage existing indexes on OrganizationId and OrgTypeIdentifier
2. **Query Optimization**: Use parameterized queries with proper joins
3. **Connection Pooling**: Use established connection management patterns
4. **Caching**: Consider caching template roles (future enhancement)

## Monitoring and Logging

1. **Error Logging**: Log all errors with appropriate context
2. **Request Logging**: Log request parameters for debugging
3. **Performance Metrics**: Track query execution times
4. **Audit Trail**: Log role creation and deletion events

## Implementation Details

### Handler Function Signatures

```go
// Handler dependencies for dependency injection
type HandlerDeps struct {
    GetConnections        func(context.Context) (*connect.Connections, error)
    GetRolesByOrganization func(connect.DatabaseExecutor, uuid.UUID) (*[]CustomRole, error)
    DeleteRole            func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
    CreateRole            func(connect.DatabaseExecutor, uuid.UUID, *CreateRoleRequest) (*CustomRole, error)
    GetRoleTemplates      func(connect.DatabaseExecutor, uuid.UUID) (*[]TemplateRole, error)
}

// HTTP Handlers with dependency injection
func GetRolesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc
func DeleteRoleHandlerWithDeps(deps HandlerDeps) http.HandlerFunc
func CreateRoleHandlerWithDeps(deps HandlerDeps) http.HandlerFunc
func GetRoleTemplatesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc

// Production handlers using default dependencies
var (
    GetRolesHandler = GetRolesHandlerWithDeps(HandlerDeps{...})
    DeleteRoleHandler = DeleteRoleHandlerWithDeps(HandlerDeps{...})
    CreateRoleHandler = CreateRoleHandlerWithDeps(HandlerDeps{...})
    GetRoleTemplatesHandler = GetRoleTemplatesHandlerWithDeps(HandlerDeps{...})
)
```

### Database Function Examples

```go
// Get roles by organization
var getRolesByOrganization = func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]CustomRole, error) {
    query := `
        SELECT
            cr.Id,
            cr.OrganizationId,
            cr.TemplateRoleIdentifier,
            cr.OrgTypeIdentifier,
            cr.Name,
            cr.Description,
            cr.CreatedAt,
            cr.UpdatedAt
        FROM {{CustomRole}} cr
        WHERE cr.OrganizationId = $1 AND cr.IsDeleted = false
        ORDER BY cr.CreatedAt DESC`

    var roles []CustomRole
    err := pg.QueryGenericSlice(&roles, query, orgId)
    if err != nil {
        logger.Errorf("Failed to get roles for organization %s: %v", orgId, err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }
    return &roles, nil
}

// Create new role with transaction
var createRole = func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *CreateRoleRequest) (*CustomRole, error) {
    // Start transaction
    tx, err := pg.BeginTx(context.Background(), nil)
    if err != nil {
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }
    defer tx.Rollback()

    // Validate organization and get OrgTypeIdentifier
    var orgTypeId string
    orgQuery := `SELECT OrgTypeIdentifier FROM {{Organization}} WHERE Id = $1 AND IsDeleted = false`
    err = tx.QueryRow(orgQuery, orgId).Scan(&orgTypeId)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, ErrOrganizationNotFound
        }
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    // Validate template role exists and matches organization type
    templateQuery := `SELECT COUNT(*) FROM {{TemplateRole}} WHERE Identifier = $1 AND OrgTypeIdentifier = $2`
    var count int
    err = tx.QueryRow(templateQuery, req.TemplateRoleIdentifier, orgTypeId).Scan(&count)
    if err != nil || count == 0 {
        return nil, ErrTemplateRoleNotFound
    }

    // Insert new custom role
    roleId := uuid.New()
    now := time.Now().UTC()

    insertQuery := `
        INSERT INTO {{CustomRole}} (
            Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier,
            Name, Description, CreatedAt, UpdatedAt, IsDeleted
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier,
                  Name, Description, CreatedAt, UpdatedAt`

    var role CustomRole
    err = tx.QueryRow(insertQuery, roleId, orgId, req.TemplateRoleIdentifier,
                     orgTypeId, req.Name, req.Description, now, now, false).Scan(
        &role.Id, &role.OrganizationId, &role.TemplateRoleIdentifier,
        &role.OrgTypeIdentifier, &role.Name, &role.Description,
        &role.CreatedAt, &role.UpdatedAt)
    if err != nil {
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    // Copy permissions from template role
    permissionQuery := `
        INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value, CreatedAt, UpdatedAt, IsDeleted)
        SELECT $1, trp.PermissionIdentifier, trp.DefaultValue, $2, $3, false
        FROM {{TemplateRolePermission}} trp
        WHERE trp.TemplateRoleIdentifier = $4 AND trp.IsDeleted = false`

    _, err = tx.Exec(permissionQuery, roleId, now, now, req.TemplateRoleIdentifier)
    if err != nil {
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    // Commit transaction
    if err = tx.Commit(); err != nil {
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return &role, nil
}
```

### Request Validation Examples

```go
// Parse and validate create role request
func parseCreateRoleRequest(r *http.Request) (*CreateRoleRequest, error) {
    var req CreateRoleRequest
    decoder := json.NewDecoder(r.Body)
    decoder.DisallowUnknownFields()

    if err := decoder.Decode(&req); err != nil {
        logger.Infof("failed to parse create role request: %v", err)
        if strings.Contains(err.Error(), "unknown field") {
            return &req, ErrUnexpectedFields
        }
        return &req, ErrInvalidRequestBody
    }

    // Validate role name
    if strings.TrimSpace(req.Name) == "" {
        return &req, ErrInvalidRoleName
    }

    // Validate description
    if strings.TrimSpace(req.Description) == "" {
        return &req, ErrInvalidDescription
    }

    // Validate template role identifier
    if strings.TrimSpace(req.TemplateRoleIdentifier) == "" {
        return &req, ErrInvalidTemplateRole
    }

    return &req, nil
}

// Validate organization UUID from path
func validateOrganizationId(r *http.Request) (uuid.UUID, error) {
    vars := mux.Vars(r)
    orgIdStr := vars["organizationId"]

    orgId, err := uuid.Parse(orgIdStr)
    if err != nil {
        return uuid.Nil, ErrInvalidOrganizationId
    }

    return orgId, nil
}
```

### Route Registration

```go
// In routes.go, add these routes:
router.HandleFunc("/api/organization/{organizationId}/roles", RestRoles.GetRolesHandler).Methods(http.MethodGet)
router.HandleFunc("/api/organization/{organizationId}/roles", RestRoles.CreateRoleHandler).Methods(http.MethodPost)
router.HandleFunc("/api/organization/{organizationId}/roles/{roleId}", RestRoles.DeleteRoleHandler).Methods(http.MethodDelete)
router.HandleFunc("/api/organization/{organizationId}/role-templates", RestRoles.GetRoleTemplatesHandler).Methods(http.MethodGet)
```

## Future Enhancements

1. **Permission Management**: Add endpoints to manage individual permissions
2. **Role Assignment**: Add endpoints to assign roles to users
3. **Role Templates**: Add CRUD operations for template roles
4. **Bulk Operations**: Support bulk role operations
5. **Role Inheritance**: Implement role hierarchy and inheritance
6. **Audit Logging**: Comprehensive audit trail for all role changes

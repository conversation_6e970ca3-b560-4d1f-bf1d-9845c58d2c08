package roles

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// MockHandlerDeps provides mock dependencies for testing
type MockHandlerDeps struct {
	mock.Mock
}

func (m *MockHandlerDeps) GetConnections(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
	args := m.Called(ctx, checkConnections)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*connect.Connections), args.Error(1)
}

func (m *MockHandlerDeps) GetRolesByOrganization(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]CustomRole, error) {
	args := m.Called(pg, orgId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*[]CustomRole), args.Error(1)
}

func (m *MockHandlerDeps) DeleteRole(pg connect.DatabaseExecutor, orgId uuid.UUID, roleId uuid.UUID) error {
	args := m.Called(pg, orgId, roleId)
	return args.Error(0)
}

func (m *MockHandlerDeps) CreateRole(pg connect.DatabaseExecutor, orgId uuid.UUID, req *CreateRoleRequest) (*CustomRole, error) {
	args := m.Called(pg, orgId, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*CustomRole), args.Error(1)
}

func (m *MockHandlerDeps) GetRoleTemplates(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]TemplateRole, error) {
	args := m.Called(pg, orgId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*[]TemplateRole), args.Error(1)
}

func TestGetRolesHandler_Success(t *testing.T) {
	// Setup
	mockDeps := &MockHandlerDeps{}
	mockDB := &dbexecutor.FakeDBExecutor{}
	
	orgId := uuid.New()
	expectedRoles := []CustomRole{
		{
			Id:                     uuid.New(),
			OrganizationId:         orgId,
			TemplateRoleIdentifier: "mun_admin",
			OrgTypeIdentifier:      "municipality",
			Name:                   "Test Admin Role",
			Description:            "Test admin role description",
		},
	}

	// Mock expectations
	mockDeps.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{
		Postgres: mockDB,
	}, nil)
	mockDeps.On("GetRolesByOrganization", mockDB, orgId).Return(&expectedRoles, nil)

	// Create handler
	handler := GetRolesHandlerWithDeps(HandlerDeps{
		GetConnections:         mockDeps.GetConnections,
		GetRolesByOrganization: mockDeps.GetRolesByOrganization,
	})

	// Create request
	req := httptest.NewRequest("GET", "/api/organization/"+orgId.String()+"/roles", nil)
	req = mux.SetURLVars(req, map[string]string{"organizationId": orgId.String()})
	w := httptest.NewRecorder()

	// Execute
	handler(w, req)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "success", response["status"])
	
	mockDeps.AssertExpectations(t)
}

func TestCreateRoleHandler_Success(t *testing.T) {
	// Setup
	mockDeps := &MockHandlerDeps{}
	mockDB := &dbexecutor.FakeDBExecutor{}
	
	orgId := uuid.New()
	roleId := uuid.New()
	
	createRequest := CreateRoleRequest{
		Name:                   "Test Role",
		TemplateRoleIdentifier: "mun_admin",
		Description:            "Test role description",
	}
	
	expectedRole := CustomRole{
		Id:                     roleId,
		OrganizationId:         orgId,
		TemplateRoleIdentifier: "mun_admin",
		OrgTypeIdentifier:      "municipality",
		Name:                   "Test Role",
		Description:            "Test role description",
	}

	// Mock expectations
	mockDeps.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{
		Postgres: mockDB,
	}, nil)
	mockDeps.On("CreateRole", mockDB, orgId, &createRequest).Return(&expectedRole, nil)

	// Create handler
	handler := CreateRoleHandlerWithDeps(HandlerDeps{
		GetConnections: mockDeps.GetConnections,
		CreateRole:     mockDeps.CreateRole,
	})

	// Create request body
	requestBody, _ := json.Marshal(createRequest)
	req := httptest.NewRequest("POST", "/api/organization/"+orgId.String()+"/roles", bytes.NewBuffer(requestBody))
	req = mux.SetURLVars(req, map[string]string{"organizationId": orgId.String()})
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Execute
	handler(w, req)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "success", response["status"])
	
	mockDeps.AssertExpectations(t)
}

func TestValidateOrganizationId_Success(t *testing.T) {
	orgId := uuid.New()
	req := httptest.NewRequest("GET", "/test", nil)
	req = mux.SetURLVars(req, map[string]string{"organizationId": orgId.String()})

	result, err := validateOrganizationId(req)
	
	assert.NoError(t, err)
	assert.Equal(t, orgId, result)
}

func TestValidateOrganizationId_InvalidUUID(t *testing.T) {
	req := httptest.NewRequest("GET", "/test", nil)
	req = mux.SetURLVars(req, map[string]string{"organizationId": "invalid-uuid"})

	result, err := validateOrganizationId(req)
	
	assert.Error(t, err)
	assert.Equal(t, ErrInvalidOrganizationId, err)
	assert.Equal(t, uuid.Nil, result)
}

func TestParseCreateRoleRequest_Success(t *testing.T) {
	createRequest := CreateRoleRequest{
		Name:                   "Test Role",
		TemplateRoleIdentifier: "mun_admin",
		Description:            "Test role description",
	}
	
	requestBody, _ := json.Marshal(createRequest)
	req := httptest.NewRequest("POST", "/test", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")

	result, err := parseCreateRoleRequest(req)
	
	assert.NoError(t, err)
	assert.Equal(t, createRequest.Name, result.Name)
	assert.Equal(t, createRequest.TemplateRoleIdentifier, result.TemplateRoleIdentifier)
	assert.Equal(t, createRequest.Description, result.Description)
}

func TestParseCreateRoleRequest_EmptyName(t *testing.T) {
	createRequest := CreateRoleRequest{
		Name:                   "",
		TemplateRoleIdentifier: "mun_admin",
		Description:            "Test role description",
	}
	
	requestBody, _ := json.Marshal(createRequest)
	req := httptest.NewRequest("POST", "/test", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")

	result, err := parseCreateRoleRequest(req)
	
	assert.Error(t, err)
	assert.Equal(t, ErrInvalidRoleName, err)
	assert.NotNil(t, result)
}

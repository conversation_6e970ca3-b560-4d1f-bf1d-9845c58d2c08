package roles

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"

	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps defines dependencies for handlers to enable dependency injection
type HandlerDeps struct {
	GetConnections         func(context.Context, ...bool) (*connect.Connections, error)
	GetRolesByOrganization func(connect.DatabaseExecutor, uuid.UUID) (*[]CustomRole, error)
	DeleteRole             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
	CreateRole             func(connect.DatabaseExecutor, uuid.UUID, *CreateRoleRequest) (*CustomRole, error)
	GetRoleTemplates       func(connect.DatabaseExecutor, uuid.UUID) (*[]TemplateRole, error)
}

// GetRolesHandlerWithDeps returns a handler for getting roles by organization with dependency injection
func GetRolesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := validateOrganizationId(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get roles for organization
		roles, err := deps.GetRolesByOrganization(pg, orgId)
		if err != nil {
			logger.Errorf("failed to get roles for organization %s: %v", orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(roles, w)
	}
}

// DeleteRoleHandlerWithDeps returns a handler for deleting a role with dependency injection
func DeleteRoleHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := validateOrganizationId(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate role ID from path
		roleId, err := validateRoleId(r)
		if err != nil {
			logger.Errorf("invalid role ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Delete the role
		err = deps.DeleteRole(pg, orgId, roleId)
		if err != nil {
			if err == ErrRoleNotFound {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to delete role %s for organization %s: %v", roleId, orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(nil, w)
	}
}

// CreateRoleHandlerWithDeps returns a handler for creating a new role with dependency injection
func CreateRoleHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := validateOrganizationId(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body with validation
		requestBody, err := parseCreateRoleRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Create the role
		role, err := deps.CreateRole(pg, orgId, requestBody)
		if err != nil {
			if err == ErrOrganizationNotFound {
				response.CreateNotFoundResponse(w)
				return
			}
			if err == ErrTemplateRoleNotFound || err == ErrTemplateRoleMismatch {
				response.CreateBadRequestResponse(w)
				return
			}
			logger.Errorf("failed to create role for organization %s: %v", orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(role, w)
	}
}

// GetRoleTemplatesHandlerWithDeps returns a handler for getting role templates with dependency injection
func GetRoleTemplatesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := validateOrganizationId(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get role templates for organization
		templates, err := deps.GetRoleTemplates(pg, orgId)
		if err != nil {
			if err == ErrOrganizationNotFound {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to get role templates for organization %s: %v", orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(templates, w)
	}
}

// validateOrganizationId validates and extracts organization ID from request path
func validateOrganizationId(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	orgIdStr := vars["organizationId"]

	orgId, err := uuid.Parse(orgIdStr)
	if err != nil {
		return uuid.Nil, ErrInvalidOrganizationId
	}

	return orgId, nil
}

// validateRoleId validates and extracts role ID from request path
func validateRoleId(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	roleIdStr := vars["roleId"]

	roleId, err := uuid.Parse(roleIdStr)
	if err != nil {
		return uuid.Nil, ErrInvalidRoleId
	}

	return roleId, nil
}

// parseCreateRoleRequest parses and validates the create role request body
func parseCreateRoleRequest(r *http.Request) (*CreateRoleRequest, error) {
	var req CreateRoleRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields()

	if err := decoder.Decode(&req); err != nil {
		logger.Infof("failed to parse create role request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return &req, ErrUnexpectedFields
		}
		return &req, ErrInvalidRequestBody
	}

	// Validate role name
	if strings.TrimSpace(req.Name) == "" {
		return &req, ErrInvalidRoleName
	}

	// Validate description
	if strings.TrimSpace(req.Description) == "" {
		return &req, ErrInvalidDescription
	}

	// Validate template role identifier
	if strings.TrimSpace(req.TemplateRoleIdentifier) == "" {
		return &req, ErrInvalidTemplateRole
	}

	return &req, nil
}

// Database functions

// getRolesByOrganization retrieves all non-deleted custom roles for an organization
var getRolesByOrganization = func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]CustomRole, error) {
	query := `
		SELECT
			cr.Id,
			cr.OrganizationId,
			cr.TemplateRoleIdentifier,
			cr.OrgTypeIdentifier,
			cr.Name,
			cr.Description,
			cr.CreatedAt,
			cr.UpdatedAt
		FROM {{CustomRole}} cr
		WHERE cr.OrganizationId = $1 AND cr.IsDeleted = false
		ORDER BY cr.CreatedAt DESC`

	var roles []CustomRole
	err := pg.QueryGenericSlice(&roles, query, orgId)
	if err != nil {
		logger.Errorf("Failed to get roles for organization %s: %v", orgId, err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &roles, nil
}

// deleteRole soft deletes a custom role
var deleteRole = func(pg connect.DatabaseExecutor, orgId uuid.UUID, roleId uuid.UUID) error {
	query := `
		UPDATE {{CustomRole}}
		SET IsDeleted = true, UpdatedAt = NOW()
		WHERE Id = $1 AND OrganizationId = $2 AND IsDeleted = false`

	result, err := pg.Exec(query, roleId, orgId)
	if err != nil {
		logger.Errorf("Failed to delete role %s for organization %s: %v", roleId, orgId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected for role deletion %s: %v", roleId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if rowsAffected == 0 {
		return ErrRoleNotFound
	}

	return nil
}

// getRoleTemplates retrieves role templates for an organization's type
var getRoleTemplates = func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]TemplateRole, error) {
	query := `
		SELECT
			tr.Identifier,
			tr.Name,
			tr.OrgTypeIdentifier,
			tr.Description
		FROM {{TemplateRole}} tr
		JOIN {{Organization}} o ON o.OrgTypeIdentifier = tr.OrgTypeIdentifier
		WHERE o.Id = $1 AND o.IsDeleted = false
		ORDER BY tr.Name ASC`

	var templates []TemplateRole
	err := pg.QueryGenericSlice(&templates, query, orgId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrOrganizationNotFound
		}
		logger.Errorf("Failed to get role templates for organization %s: %v", orgId, err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &templates, nil
}

// createRole creates a new custom role
var createRole = func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *CreateRoleRequest) (*CustomRole, error) {
	// Validate organization and get OrgTypeIdentifier
	orgQuery := `SELECT OrgTypeIdentifier FROM {{Organization}} WHERE Id = $1 AND IsDeleted = false`
	orgResult, err := pg.QueryRow(orgQuery, orgId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrOrganizationNotFound
		}
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	orgTypeId, err := connect.GetColumn[string](orgResult, "orgtypeidentifier")
	if err != nil {
		return nil, ErrOrganizationNotFound
	}

	// Validate template role exists and matches organization type
	templateQuery := `SELECT COUNT(*) FROM {{TemplateRole}} WHERE Identifier = $1 AND OrgTypeIdentifier = $2`
	templateResult, err := pg.QueryRow(templateQuery, req.TemplateRoleIdentifier, orgTypeId)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	count, err := connect.GetColumn[int](templateResult, "count")
	if err != nil || count == 0 {
		return nil, ErrTemplateRoleNotFound
	}

	// Insert new custom role
	roleId := uuid.New()
	now := time.Now().UTC()

	insertQuery := `
		INSERT INTO {{CustomRole}} (
			Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier,
			Name, Description, CreatedAt, UpdatedAt, IsDeleted
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier,
				  Name, Description, CreatedAt, UpdatedAt`

	var role CustomRole
	err = pg.QueryRowStruct(&role, insertQuery, roleId, orgId, req.TemplateRoleIdentifier,
		orgTypeId, req.Name, req.Description, now, now, false)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Copy permissions from template role
	permissionQuery := `
		INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value, CreatedAt, UpdatedAt, IsDeleted)
		SELECT $1, trp.PermissionIdentifier, trp.DefaultValue, $2, $3, false
		FROM {{TemplateRolePermission}} trp
		WHERE trp.TemplateRoleIdentifier = $4 AND trp.IsDeleted = false`

	_, err = pg.Exec(permissionQuery, roleId, now, now, req.TemplateRoleIdentifier)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &role, nil
}

// Production handlers using default dependencies
var (
	GetRolesHandler = GetRolesHandlerWithDeps(HandlerDeps{
		GetConnections:         connect.GetConnections,
		GetRolesByOrganization: getRolesByOrganization,
	})
	DeleteRoleHandler = DeleteRoleHandlerWithDeps(HandlerDeps{
		GetConnections: connect.GetConnections,
		DeleteRole:     deleteRole,
	})
	CreateRoleHandler = CreateRoleHandlerWithDeps(HandlerDeps{
		GetConnections: connect.GetConnections,
		CreateRole:     createRole,
	})
	GetRoleTemplatesHandler = GetRoleTemplatesHandlerWithDeps(HandlerDeps{
		GetConnections:   connect.GetConnections,
		GetRoleTemplates: getRoleTemplates,
	})
)

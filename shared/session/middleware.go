package session

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/coreos/go-oidc"
	"golang.org/x/oauth2"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/logger"
)

// SessionMiddleware handles session validation and context injection
type SessionMiddleware struct {
	store        SessionStore
	cookieName   string
	oidcVerifier *oidc.IDTokenVerifier
	skipVerify   bool // Option to skip ID token verification for performance
}

// NewSessionMiddleware creates a new session middleware
func NewSessionMiddleware(store SessionStore, cookieName string, verifier *oidc.IDTokenVerifier) *SessionMiddleware {
	return &SessionMiddleware{
		store:        store,
		cookieName:   cookieName,
		oidcVerifier: verifier,
		skipVerify:   false,
	}
}

// WithSkipVerification creates middleware that skips ID token verification
func (sm *SessionMiddleware) WithSkipVerification() *SessionMiddleware {
	newSM := *sm
	newSM.skipVerify = true
	return &newSM
}

// Middleware returns the HTTP middleware function
func (sm *SessionMiddleware) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract session ID from cookie
		cookie, err := r.Cookie(sm.cookieName)
		if err != nil {
			http.Error(w, "unauthorized: no session cookie", http.StatusUnauthorized)
			return
		}

		// Get session data
		sessionData, err := sm.store.Get(cookie.Value)
		if err != nil {
			if err == ErrSessionNotFound || err == ErrSessionExpired {
				http.Error(w, "unauthorized: invalid session", http.StatusUnauthorized)
				return
			}
			logger.Errorf("Session store error: %v", err)
			http.Error(w, "internal server error", http.StatusInternalServerError)
			return
		}

		var claims map[string]interface{}

		// Verify ID token if not skipped
		if !sm.skipVerify && sm.oidcVerifier != nil {
			claims, err = sm.verifyIDToken(ctx, sessionData.OAuthToken)
			if err != nil {
				logger.Errorf("ID token verification failed: %v", err)
				http.Error(w, "unauthorized: invalid token", http.StatusUnauthorized)
				return
			}
		}

		// Add session data to context
		ctx = context.WithValue(ctx, SessionDataKey, sessionData)
		ctx = context.WithValue(ctx, UserPermissionsKey, sessionData.UserPermissions)
		if claims != nil {
			ctx = context.WithValue(ctx, UserClaimsKey, claims)
		}

		// Continue to next handler
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// verifyIDToken verifies the OIDC ID token
func (sm *SessionMiddleware) verifyIDToken(ctx context.Context, token *oauth2.Token) (map[string]interface{}, error) {
	rawID, ok := token.Extra("id_token").(string)
	if !ok {
		return nil, errors.New("no id_token in oauth token")
	}

	idToken, err := sm.oidcVerifier.Verify(ctx, rawID)
	if err != nil {
		return nil, err
	}

	var claims map[string]interface{}
	if err := idToken.Claims(&claims); err != nil {
		return nil, err
	}

	return claims, nil
}

// CreateSession creates a new session with the provided data
func CreateSession(store SessionStore, sessionID string, token *oauth2.Token, userID string, userPermissions *authorizer.UserPermissions) error {
	sessionData := &SessionData{
		OAuthToken:      token,
		UserID:          userID,
		UserPermissions: userPermissions,
		CreatedAt:       time.Now(),
		LastAccessedAt:  time.Now(),
		ExpiresAt:       time.Now().Add(24 * time.Hour), // 24 hour default
	}

	return store.Set(sessionID, sessionData)
}

// CreateSessionWithTTL creates a new session with custom TTL
func CreateSessionWithTTL(store SessionStore, sessionID string, token *oauth2.Token, userID string, userPermissions *authorizer.UserPermissions, ttl time.Duration) error {
	sessionData := &SessionData{
		OAuthToken:      token,
		UserID:          userID,
		UserPermissions: userPermissions,
		CreatedAt:       time.Now(),
		LastAccessedAt:  time.Now(),
		ExpiresAt:       time.Now().Add(ttl),
	}

	return store.Set(sessionID, sessionData)
}

// Helper functions for extracting data from context

// GetSessionData extracts session data from context
func GetSessionData(ctx context.Context) (*SessionData, bool) {
	data, ok := ctx.Value(SessionDataKey).(*SessionData)
	return data, ok
}

// GetUserPermissions extracts user permissions from context
func GetUserPermissions(ctx context.Context) (*authorizer.UserPermissions, bool) {
	perms, ok := ctx.Value(UserPermissionsKey).(*authorizer.UserPermissions)
	return perms, ok
}

// GetUserClaims extracts user claims from context
func GetUserClaims(ctx context.Context) (map[string]interface{}, bool) {
	claims, ok := ctx.Value(UserClaimsKey).(map[string]interface{})
	return claims, ok
}

// GetUserIDFromSession extracts user ID from session data in context
func GetUserIDFromSession(ctx context.Context) (string, bool) {
	sessionData, ok := GetSessionData(ctx)
	if !ok {
		return "", false
	}
	return sessionData.UserID, true
}

// RefreshSession updates the session's last accessed time and optionally extends expiration
func RefreshSession(store SessionStore, sessionID string, extendExpiration bool, ttl time.Duration) error {
	sessionData, err := store.Get(sessionID)
	if err != nil {
		return err
	}

	sessionData.Touch()
	if extendExpiration {
		sessionData.ExpiresAt = time.Now().Add(ttl)
	}

	return store.Set(sessionID, sessionData)
}

package main

import (
	"context"
	_ "embed"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/coreos/go-oidc"
	"github.com/gorilla/mux"
	"golang.org/x/oauth2"

	"synapse-its.com/onramp/handlers"
	"synapse-its.com/onramp/handlers/assets"
	"synapse-its.com/shared/api/middleware"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/httplogger"
	"synapse-its.com/shared/logger"
	RestOrganization "synapse-its.com/shared/rest/onramp/organization"
	RestRoles "synapse-its.com/shared/rest/onramp/roles"
	RestSoftwareGateway "synapse-its.com/shared/rest/onramp/softwaregateway"
	RestSoftwareGatewayConfig "synapse-its.com/shared/rest/onramp/softwaregateway/config"
)

// NewRouter initializes the router and registers routes.
func NewRouter(connections *connect.Connections, batch bqbatch.Batcher) *mux.Router {
	router := mux.NewRouter()

	// Apply middleware globally (e.g., logging, authentication).
	router.Use(middleware.ConnectionsMiddleware(connections))
	router.Use(middleware.BQBatchMiddleware(batch))
	router.Use(httplogger.LoggingMiddleware)

	// These endpoints for full CRUD operations for organization management
	router.HandleFunc("/api/organizations", RestOrganization.CreateHandler).Methods(http.MethodPost)
	router.HandleFunc("/api/organizations", RestOrganization.GetAllHandler).Methods(http.MethodGet)
	router.HandleFunc("/api/organizations/{identifier}", RestOrganization.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/api/organizations/{identifier}", RestOrganization.UpdateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/api/organizations/{identifier}", RestOrganization.DeleteHandler).Methods(http.MethodDelete)

	// These endpoints for full CRUD operations for software gateway management
	router.HandleFunc("/api/softwaregateway", RestSoftwareGateway.CreateHandler).Methods(http.MethodPost)
	router.HandleFunc("/api/softwaregateway", RestSoftwareGateway.GetAllHandler).Methods(http.MethodGet)
	router.HandleFunc("/api/softwaregateway/{identifier}", RestSoftwareGateway.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/api/softwaregateway/{identifier}", RestSoftwareGateway.UpdateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/api/softwaregateway/{identifier}", RestSoftwareGateway.DeleteHandler).Methods(http.MethodDelete)

	// These endpoints for read and update operations for software gateway config
	router.HandleFunc("/api/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/api/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.UpdateHandler).Methods(http.MethodPatch)

	// These endpoints for roles management
	router.HandleFunc("/api/organization/{organizationId}/roles", RestRoles.GetRolesHandler).Methods(http.MethodGet)
	router.HandleFunc("/api/organization/{organizationId}/roles", RestRoles.CreateRoleHandler).Methods(http.MethodPost)
	router.HandleFunc("/api/organization/{organizationId}/roles/{roleId}", RestRoles.DeleteRoleHandler).Methods(http.MethodDelete)
	router.HandleFunc("/api/organization/{organizationId}/role-templates", RestRoles.GetRoleTemplatesHandler).Methods(http.MethodGet)

	// Bare-bones /foo endpoint returning ["Alice","Bob"]
	router.HandleFunc("/foo", handlers.FooHandler).Methods(http.MethodGet)
	router.HandleFunc("/gateway", handlers.GatewayHandler).Methods(http.MethodGet)
	router.HandleFunc("/gateway-config", handlers.ConfigGatewayHandler).Methods(http.MethodGet)
	router.HandleFunc("/devices", handlers.DevicesHandler).Methods(http.MethodGet)

	// /api endpoints
	apiRouter := router.PathPrefix("/api").Subrouter()
	apiRouter.HandleFunc("/foo", handlers.FooHandler).Methods(http.MethodGet)
	apiRouter.HandleFunc("/gateway", handlers.GatewayHandler).Methods(http.MethodGet)
	apiRouter.HandleFunc("/gateway-config", handlers.ConfigGatewayHandler).Methods(http.MethodGet)
	apiRouter.HandleFunc("/devices", handlers.DevicesHandler).Methods(http.MethodGet)

	// Define default endpoints
	router.HandleFunc("/assets/env.js", assets.EnvJsHandler)
	fs := http.FileServer(http.Dir("./static/browser"))

	// Login/Callback endpoints - commented out for roles-only implementation
	// router.HandleFunc("/login", handleLogin).Methods(http.MethodGet)
	// router.HandleFunc("/callback", handleCallback).Methods(http.MethodGet)
	// router.HandleFunc("/logout", handleLogout).Methods(http.MethodGet)

	// Protected routes using new session middleware - commented out for roles-only implementation
	// protectedRouter := router.PathPrefix("/protected").Subrouter()
	// protectedRouter.Use(sessionManager.GetMiddleware())
	// protectedRouter.HandleFunc("/foo", handlers.FooHandler).Methods(http.MethodGet)
	// protectedRouter.HandleFunc("/profile", handleProfile).Methods(http.MethodGet)

	// The catch-all (file system server) must be last.
	router.PathPrefix("/").Handler(fs)
	return router
}

type UserProfile struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

// OIDCConfig holds the configuration for OpenID Connect (OIDC) authentication.
type OIDCConfig struct {
	ClientID     string
	ClientSecret string
	RedirectURL  string
	IssuerURL    string
	Provider     *oidc.Provider
	OAuth2Config *oauth2.Config
	Verifier     *oidc.IDTokenVerifier
	Scope        string
}

var (
	// synapseOIDC is a global variable that holds the OIDC configuration
	// for the Synapse OAuth2 server.
	synapseOIDC = OIDCConfig{
		ClientID:     os.Getenv("SYNAPSE_OIDC_CLIENT_ID"),
		ClientSecret: os.Getenv("SYNAPSE_OIDC_CLIENT_SECRET"),
		RedirectURL:  os.Getenv("SYNAPSE_OIDC_CLIENT_CALLBACK_URL"),
		IssuerURL:    os.Getenv("SYNAPSE_OIDC_ISSUER_URL"),
	}

	// synapseOIDCLocal is a local version of the OIDC configuration
	// used for development purposes, pointing to keycloak.
	synapseOIDCLocal OIDCConfig

	synapseOIDCScopes = []string{oidc.ScopeOpenID, "profile", "email"}

	// Global session manager - commented out for roles-only implementation
	// sessionManager *session.SessionManager

	// localhostHTTPProxy is a custom HTTP client that rewrites requests
	// to "localhost:8091" to "host.docker.internal:8091". This is necessary
	// for the OIDC provider to communicate with the host machine from within
	// a Docker container, as Docker containers cannot directly access services
	// running on the host machine using "localhost".  This is not a security
	// concern in prod, because in production, the request will simply fail
	// because there is no OIDC provider listening there.
	localhostHTTPProxy = &http.Client{Transport: &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			if strings.HasSuffix(addr, "localhost:8091") {
				addr = "host.docker.internal:8091"
			}
			return (&net.Dialer{}).DialContext(ctx, network, addr)
		},
	}}
)

// Initialize all OIDC configurations.
func init() {
	ctx := context.Background()

	// Real Synapse OIDC configuration
	var err error
	start := time.Now()
	for {
		synapseOIDC.Provider, err = oidc.NewProvider(ctx, synapseOIDC.IssuerURL)
		if err != nil {
			logger.Warnf("failed to init real OIDC provider (OK for testing, bad for prod): %v", err)
			time.Sleep(time.Second)
			continue
		}
		break
	}
	logger.Info(time.Since(start))

	synapseOIDC.Verifier = synapseOIDC.Provider.Verifier(&oidc.Config{
		ClientID: synapseOIDC.ClientID,
	})
	synapseOIDC.OAuth2Config = &oauth2.Config{
		ClientID:     synapseOIDC.ClientID,
		ClientSecret: synapseOIDC.ClientSecret,
		Endpoint:     synapseOIDC.Provider.Endpoint(),
		RedirectURL:  synapseOIDC.RedirectURL,
		Scopes:       synapseOIDCScopes,
	}

	// Local Synapse OIDC configuration for development
	synapseOIDCLocal = synapseOIDC

	// Override only the URLs for dev.  In production, the URLs won't contain
	// "onramp" or "keycloak", so they won't change.
	synapseOIDCLocal.IssuerURL = strings.ReplaceAll(
		synapseOIDC.IssuerURL,
		"keycloak:8080",
		"localhost:8091",
	)
	synapseOIDCLocal.RedirectURL = strings.ReplaceAll(
		synapseOIDC.RedirectURL,
		"onramp:4200",
		"localhost:4200",
	)

	// Re-init provider/verifier/oauth2.Config for localhost
	synapseOIDCLocal.Provider, err = oidc.NewProvider(oidc.ClientContext(ctx, localhostHTTPProxy), synapseOIDCLocal.IssuerURL)
	if err != nil {
		// In production, this will error because there is no OIDC provider
		// listening on localhost:8091.  `synapseOIDCLocal` is a copy of the actual
		// OIDC provider because it is not overwritten here.  If someone tries to
		// send us forged host headers, the worst that will happen is that nothing
		// will validate, which is what we want.
		logger.Warnf("failed to init local OIDC provider: %v", err)
	} else {
		// If we made it here, then the provider was able to talk to the OIDC
		// server on localhost.
		synapseOIDCLocal.Verifier = synapseOIDCLocal.Provider.Verifier(&oidc.Config{
			ClientID: synapseOIDCLocal.ClientID,
		})
		synapseOIDCLocal.OAuth2Config = &oauth2.Config{
			ClientID:     synapseOIDCLocal.ClientID,
			ClientSecret: synapseOIDCLocal.ClientSecret,
			Endpoint:     synapseOIDCLocal.Provider.Endpoint(),
			RedirectURL:  synapseOIDCLocal.RedirectURL,
			Scopes:       synapseOIDCScopes,
		}
	}

	// Initialize session manager with the production OIDC verifier - commented out for roles-only implementation
	// sessionManager = session.NewSessionManager(synapseOIDC.Verifier)
}

// Session store is now managed by sessionManager

// Commented out for roles-only implementation
/*
func handleLogin(w http.ResponseWriter, r *http.Request) {
	isDev := strings.HasPrefix(r.Host, "localhost:4200")
	state := util.RandomString(32)

	// Set the state cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    state,
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		SameSite: http.SameSiteLaxMode,
	})

	// Choose the OIDC configuration based on the request host
	cfg := map[bool]*oauth2.Config{
		false: synapseOIDC.OAuth2Config,
		true:  synapseOIDCLocal.OAuth2Config,
	}[isDev]

	// Redirect to the OIDC provider's authorization endpoint
	http.Redirect(w, r, cfg.AuthCodeURL(state), http.StatusFound)
}
*/

/*
func handleCallback(w http.ResponseWriter, r *http.Request) {
	ctx := oidc.ClientContext(r.Context(), localhostHTTPProxy)
	isDev := strings.HasPrefix(r.Host, "localhost:4200")

	// Verify state cookie
	st, err := r.Cookie("oauth_state")
	if err != nil || r.URL.Query().Get("state") != st.Value {
		http.Error(w, "invalid state", http.StatusBadRequest)
		return
	}

	// Delete the state cookie so it can’t be reused
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Secure:   !isDev,
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		SameSite: http.SameSiteLaxMode,
	})

	// Choose the OIDC configuration based on the request host
	oidcConfig := map[bool]*OIDCConfig{
		false: &synapseOIDC,
		true:  &synapseOIDCLocal,
	}[isDev]

	// Exchange code for token
	token, err := oidcConfig.OAuth2Config.Exchange(ctx, r.URL.Query().Get("code"))
	if err != nil {
		http.Error(w, "token exchange failed: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// Verify the ID Token
	rawID := token.Extra("id_token").(string)
	idToken, err := oidcConfig.Verifier.Verify(ctx, rawID)
	if err != nil {
		http.Error(w, "ID token invalid: "+err.Error(), http.StatusUnauthorized)
		return
	}

	// Extract claims to get user ID
	var claims map[string]interface{}
	if err := idToken.Claims(&claims); err != nil {
		http.Error(w, "cannot read claims", http.StatusInternalServerError)
		return
	}

	// Extract user ID from claims
	userID, ok := claims["sub"].(string)
	if !ok {
		http.Error(w, "no user ID in claims", http.StatusInternalServerError)
		return
	}

	// Get database connection
	connections, err := connect.GetConnections(ctx)
	if err != nil {
		logger.Errorf("Failed to get database connections: %v", err)
		http.Error(w, "internal server error", http.StatusInternalServerError)
		return
	}

	// Create session using the session manager
	sessionID, err := sessionManager.CreateSessionFromCallbackWithUserID(ctx, token, userID, connections.Postgres)
	if err != nil {
		logger.Errorf("Failed to create session: %v", err)
		http.Error(w, "session creation failed", http.StatusInternalServerError)
		return
	}

	// Set session cookie
	sessionManager.SetSessionCookie(w, r, sessionID)

	http.Redirect(w, r, "/", http.StatusFound)
}
*/

/*
func handleLogout(w http.ResponseWriter, r *http.Request) {
	// Get session ID from cookie
	sessionID, err := sessionManager.GetSessionFromCookie(r)
	if err == nil {
		// Delete server-side session
		err = sessionManager.DeleteSession(sessionID)
		if err != nil {
			logger.Errorf("Failed to delete session: %v", err)
		}
	}

	// Clear session cookie
	sessionManager.ClearSessionCookie(w, r)

	// Redirect to its end-session endpoint:
	// NOTE: This would log the user out of the OIDC provider, but it is not
	// strictly necessary for our use case, since we are not using the OIDC
	// provider for anything other than authentication.  This code is left here
	// for reference, in case we want to implement a full logout flow in the
	// future.
	//
	// redirectURI := url.QueryEscape(oidcConfig.RedirectURL)
	// logoutURL := oidcConfig.IssuerURL +
	// 	"/protocol/openid-connect/logout?redirect_uri=" + redirectURI
	// http.Redirect(w, r, logoutURL, http.StatusFound)

	http.Redirect(w, r, "/", http.StatusFound)
}

func handleProfile(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user claims from context (injected by session middleware)
	claims, ok := session.GetUserClaims(ctx)
	if !ok {
		http.Error(w, "no claims available", http.StatusUnauthorized)
		return
	}

	// Get user permissions from context
	userPermissions, ok := session.GetUserPermissions(ctx)
	if !ok {
		http.Error(w, "no permissions available", http.StatusUnauthorized)
		return
	}

	// Get session data
	sessionData, ok := session.GetSessionData(ctx)
	if !ok {
		http.Error(w, "no session data available", http.StatusUnauthorized)
		return
	}

	// Extract the name and email from the claims.
	name, okName := claims["name"].(string)
	name = map[bool]string{
		true:  name,
		false: "",
	}[okName]
	email, okEmail := claims["email"].(string)
	email = map[bool]string{
		true:  email,
		false: "",
	}[okEmail]

	profile := UserProfile{
		Name:  name,
		Email: email,
	}

	// If no email address, then we don't return the profile.
	if email == "" {
		http.Error(w, "profile not found", http.StatusNotFound)
		return
	}

	// Log session information for debugging
	logger.Infof("Profile request for user %s, session created at %v",
		sessionData.UserID, sessionData.CreatedAt)

	// You can also access user permissions here
	logger.Infof("User has %d permissions", len(userPermissions.Permissions))

	// Return the profile as JSON
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(profile)
}
*/
